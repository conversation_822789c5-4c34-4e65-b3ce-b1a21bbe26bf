{"ResponseMetadata": {"RequestId": "20250828030332DE8C105FCCD85791FCF6", "Region": "CN", "HasPass": true, "Service": "PlatformFE"}, "Result": {"BusinessID": 93460, "Content": "普通上传是指通过 PutObjectV2 方法上传单个对象(Object)，支持上传字符串（字符流）、上传 Bytes（Bytes 流）、上传网络流和上传本地文件四种形式。\n<span id=\"注意事项\"></span>\n## **注意事项**\n\n* 上传对象前，您必须具有 `tos:PutObject` 权限，具体操作，请参见[权限配置指南](/docs/6349/102120)。\n* 上传对象时，对象名必须满足一定规范，详细信息，请参见[对象命名规范](/docs/6349/74822)。\n* TOS 是面向海量存储设计的分布式对象存储产品，内部分区存储了对象索引数据。为横向扩展您上传对象和下载对象时的最大吞吐量和减小热点分区的概率，请您避免使用字典序递增的对象命名方式，详细信息，请参见[性能优化](/docs/6349/155630)。\n* 如果桶中已经存在同名对象，则新对象会覆盖已有的对象。如果您的桶开启了版本控制，则会保留原有对象，并生成一个新版本号用于标识新上传的对象。\n\n<span id=\"示例代码\"></span>\n## **示例代码**\n<span id=\"上传字符串\"></span>\n### **上传字符串**\n您可以通过以下示例代码，使用 PutObjectV2 接口，上传字符串数据到 TOS 指定 `example_dir` 目录下的 `example.txt` 文件。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n   \"strings\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   // 将字符串 “Hello TOS” 上传到指定 example_dir 目录下的 example.txt\n   body := strings.NewReader(\"Hello TOS\")\n   output, err := client.PutObjectV2(ctx, &tos.PutObjectV2Input{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket: bucketName,\n         Key:    objectKey,\n      },\n      Content: body,\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"上传网络流\"></span>\n### **上传网络流**\n您可以通过以下示例代码，使用 PutObjectV2 接口上传网络流数据到 TOS 指定 `example_dir` 目录下的 `example.txt` 文件。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n   \"net/http\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   // 从网络流中获取数据\n   res, _ := http.Get(\"https://www.volcengine.com/\")\n   defer res.Body.Close()\n   output, err := client.PutObjectV2(ctx, &tos.PutObjectV2Input{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket: bucketName,\n         Key:    objectKey,\n      },\n      Content: res.Body,\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"上传本地文件流\"></span>\n### **上传本地文件流**\n您可以通过以下示例代码，使用 PutObjectV2 接口，将指定路径上的文件上传到 TOS 指定 `example_dir` 目录下的 `example.txt` 文件。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n   \"os\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      fileName = \"/usr/local/test.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   // 读取本地文件数据\n   f, err := os.Open(\"./example.txt\")\n   if err != nil {\n      panic(err)\n   }\n   defer f.Close()\n   output, err := client.PutObjectV2(ctx, &tos.PutObjectV2Input{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket: bucketName,\n         Key:    objectKey,\n      },\n      Content: f,\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"从本地文件上传\"></span>\n### **从本地文件上传**\n您可以通过以下示例代码，使用 PutObjectFromFile 接口，通过指定文件路径将文件上传到 TOS 指定 `example_dir` 目录下的 `example.txt` 文件。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   // 直接使用文件路径上传文件\n   output, err := client.PutObjectFromFile(ctx, &tos.PutObjectFromFileInput{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket: bucketName,\n         Key:    objectKey,\n      },\n      FilePath: \"./example.txt\",\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"上传时设置对象元数据信息\"></span>\n### **上传时设置对象元数据信息**\n您可以通过以下示例代码，使用 PutObjectV2 接口，上传字符串数据到指定 `example_dir` 目录下的 `example.txt` 文件，上传时指定对象存储类型为低频存储，权限为私有同时设置上传文件元数据信息。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n   \"strings\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos/enum\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   // 将字符串 “Hello TOS” 上传到指定 example_dir 目录下的 example.txt\n   body := strings.NewReader(\"Hello TOS\")\n   output, err := client.PutObjectV2(ctx, &tos.PutObjectV2Input{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket: bucketName,\n         Key:    objectKey,\n         // 指定存储类型为低频存储\n         StorageClass: enum.StorageClassIa,\n         // 指定对象权限为私有\n         ACL: enum.ACLPrivate,\n         // 用户自定义元数据信息\n         Meta: map[string]string{\"key\": \"value\"},\n      },\n      Content: body,\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"配置进度条\"></span>\n### **配置进度条**\n上传时可通过实现 tos.DataTransferStatusChange 接口接收上传进度，代码示例如下。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n   \"strings\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos/enum\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\n// 自定义进度回调，需要实现 tos.DataTransferStatusChange 接口\ntype listener struct {\n}\n\nfunc (l *listener) DataTransferStatusChange(event *tos.DataTransferStatus) {\n   switch event.Type {\n   case enum.DataTransferStarted:\n      fmt.Println(\"Data transfer started\")\n   case enum.DataTransferRW:\n      // Chunk 模式下 TotalBytes 值为 -1\n      if event.TotalBytes != -1 {\n         fmt.Printf(\"Once Read:%d,ConsumerBytes/TotalBytes: %d/%d,%d%%\\n\", event.RWOnceBytes, event.ConsumedBytes, event.TotalBytes, event.ConsumedBytes*100/event.TotalBytes)\n      } else {\n         fmt.Printf(\"Once Read:%d,ConsumerBytes:%d\\n\", event.RWOnceBytes, event.ConsumedBytes)\n      }\n   case enum.DataTransferSucceed:\n      fmt.Printf(\"Data Transfer Succeed, ConsumerBytes/TotalBytes: %d/%d,%d%%\\n\", event.ConsumedBytes, event.TotalBytes, event.ConsumedBytes*100/event.TotalBytes)\n   case enum.DataTransferFailed:\n      fmt.Printf(\"Data Transfer Failed\\n\")\n   }\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   // 将字符串 “Hello TOS” 上传到指定 example_dir 目录下的 example.txt\n   body := strings.NewReader(\"Hello TOS\")\n   output, err := client.PutObjectV2(ctx, &tos.PutObjectV2Input{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket: bucketName,\n         Key:    objectKey,\n         // 通过自定义方式设置回调函数查看上传进度\n         DataTransferListener: &listener{},\n      },\n      Content: body,\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"配置客户端限速\"></span>\n### **配置客户端限速**\n上传对象时可以通过客户端使用 tos.RateLimiter 接口对上传数据所占用的带宽进行限制，代码如下所示。\n```go\npackage main\n\nimport (\n   \"context\"\n   \"fmt\"\n   \"strings\"\n   \"sync\"\n   \"time\"\n\n   \"github.com/volcengine/ve-tos-golang-sdk/v2/tos\"\n)\n\nfunc checkErr(err error) {\n   if err != nil {\n      if serverErr, ok := err.(*tos.TosServerError); ok {\n         fmt.Println(\"Error:\", serverErr.Error())\n         fmt.Println(\"Request ID:\", serverErr.RequestID)\n         fmt.Println(\"Response Status Code:\", serverErr.StatusCode)\n         fmt.Println(\"Response Header:\", serverErr.Header)\n         fmt.Println(\"Response Err Code:\", serverErr.Code)\n         fmt.Println(\"Response Err Msg:\", serverErr.Message)\n      } else if clientErr, ok := err.(*tos.TosClientError); ok {\n         fmt.Println(\"Error:\", clientErr.Error())\n         fmt.Println(\"Client Cause Err:\", clientErr.Cause.Error())\n      } else {\n         fmt.Println(\"Error:\", err)\n      }\n      panic(err)\n   }\n}\n\ntype rateLimit struct {\n   rate     int64\n   capacity int64\n\n   currentAmount int64\n   sync.Mutex\n   lastConsumeTime time.Time\n}\n\nfunc NewDefaultRateLimit(rate int64, capacity int64) tos.RateLimiter {\n   return &rateLimit{\n      rate:            rate,\n      capacity:        capacity,\n      lastConsumeTime: time.Now(),\n      currentAmount:   capacity,\n      Mutex:           sync.Mutex{},\n   }\n}\n\nfunc (d *rateLimit) Acquire(want int64) (ok bool, timeToWait time.Duration) {\n   d.Lock()\n   defer d.Unlock()\n   if want > d.capacity {\n      want = d.capacity\n   }\n   increment := int64(time.Now().Sub(d.lastConsumeTime).Seconds() * float64(d.rate))\n   if increment+d.currentAmount > d.capacity {\n      d.currentAmount = d.capacity\n   } else {\n      d.currentAmount += increment\n   }\n   if want > d.currentAmount {\n      timeToWaitSec := float64(want-d.currentAmount) / float64(d.rate)\n      return false, time.Duration(timeToWaitSec * float64(time.Second))\n   }\n   d.lastConsumeTime = time.Now()\n   d.currentAmount -= want\n   return true, 0\n}\n\nfunc main() {\n   var (\n      accessKey = os.Getenv(\"TOS_ACCESS_KEY\")\n      secretKey = os.Getenv(\"TOS_SECRET_KEY\")\n      // Bucket 对应的 Endpoint，以华北2（北京）为例：https://tos-cn-beijing.volces.com\n      endpoint = \"https://tos-cn-beijing.volces.com\"\n      region   = \"cn-beijing\"\n      // 填写 BucketName\n      bucketName = \"*** Provide your bucket name ***\"\n\n      // 将文件上传到 example_dir 目录下的 example.txt 文件\n      objectKey = \"example_dir/example.txt\"\n      ctx       = context.Background()\n   )\n   // 初始化客户端\n   client, err := tos.NewClientV2(endpoint, tos.WithRegion(region), tos.WithCredentials(tos.NewStaticCredentials(accessKey, secretKey)))\n   checkErr(err)\n   body := strings.NewReader(\"Hello TOS\")\n   rateLimit1M := 1024 * 1024\n   // 上传对象并在客户端限制上传速度为 1M/s\n   output, err := client.PutObjectV2(ctx, &tos.PutObjectV2Input{\n      PutObjectBasicInput: tos.PutObjectBasicInput{\n         Bucket:      bucketName,\n         Key:         objectKey,\n         RateLimiter: NewDefaultRateLimit(int64(rateLimit1M), int64(rateLimit1M)),\n      },\n      Content: body,\n   })\n   checkErr(err)\n   fmt.Println(\"PutObjectV2 Request ID:\", output.RequestID)\n}\n```\n\n<span id=\"相关文档\"></span>\n## **相关文档**\n关于上传对象的 API 文档，请参见 [PutObject](/docs/6349/74860)。\n\n", "ContentType": "md", "CreatedTime": "2021-12-25T07:35:19Z", "DocumentCode": "", "DocumentID": 93460, "EnContent": "", "EnTitle": "普通上传（Go SDK）", "FirstPublishedTime": "2021-12-31T09:38:36Z", "HasFavorite": false, "Index": 6169933883403, "Keywords": "对象存储", "Language": "zh", "LibraryCode": "", "LibraryID": 6349, "MDContent": "", "ParentCode": "", "ParentID": 93459, "RedirectInfo": null, "ReuseDocumentVersionList": [], "Status": 2, "Title": "普通上传（Go SDK）", "Type": 0, "UpdatedTime": "2024-02-04T10:30:55Z"}}