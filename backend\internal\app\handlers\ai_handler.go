package handlers

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"fishing-api/configs"
	"fishing-api/internal/app/dto"
	"fishing-api/internal/infrastructure/external"
	"fishing-api/internal/services"

	"github.com/gin-gonic/gin"
)

type AIHandler struct {
	fishialURL        string
	volcEngineService *services.VolcEngineAIService
}

func NewAIHandler() *AIHandler {
	return &AIHandler{
		fishialURL:        "http://localhost:5001", // Fish识别服务地址
		volcEngineService: services.NewVolcEngineAIService(),
	}
}

// FishIdentificationResponse 鱼类识别响应结构
type FishIdentificationResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		Filename         string `json:"filename"`
		OriginalFilename string `json:"original_filename"`
		FileSize         int64  `json:"file_size"`
		Identification   struct {
			SpeciesID      int     `json:"species_id"`
			SpeciesName    string  `json:"species_name"`
			EnglishName    string  `json:"english_name"`
			ScientificName string  `json:"scientific_name"`
			Confidence     float64 `json:"confidence"`
			ProcessingTime float64 `json:"processing_time"`
		} `json:"identification"`
		ProcessedAt string `json:"processed_at"`
	} `json:"data"`
}

// BackgroundRemovalResponse 背景移除响应结构
type BackgroundRemovalResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
	Data    struct {
		OriginalFilename  string  `json:"original_filename"`
		ProcessedFilename string  `json:"processed_filename"`
		OriginalURL       string  `json:"original_url"`
		ProcessedURL      string  `json:"processed_url"`
		ProcessingTime    float64 `json:"processing_time"`
		ProcessedAt       string  `json:"processed_at"`
	} `json:"data"`
}

// IdentifyFish 鱼类识别接口 - 先上传到TOS，再用图片URL调用火山引擎
func (h *AIHandler) IdentifyFish(c *gin.Context) {
	// 检查文件大小
	if c.Request.ContentLength > configs.AppConfig.Upload.MaxUploadSize {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("File too large", "FILE_TOO_LARGE",
			fmt.Sprintf("Maximum file size is %d bytes", configs.AppConfig.Upload.MaxUploadSize)))
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("No file uploaded", "NO_FILE", err.Error()))
		return
	}

	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	isValidExt := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			isValidExt = true
			break
		}
	}

	if !isValidExt {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid file type", "INVALID_FILE_TYPE",
			"Only JPG, PNG, GIF, and WebP files are allowed"))
		return
	}

	// 打开文件
	src, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to read file", "FILE_READ_ERROR", err.Error()))
		return
	}
	defer src.Close()

	// 上传到 TOS，获取 URL
	objectKey := external.BuildObjectKey(fmt.Sprintf("%d_%s", time.Now().Unix(), file.Filename))
	imageURL, savedKey, err := external.UploadImage(c.Request.Context(), objectKey, src)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to upload to object storage", "TOS_UPLOAD_ERROR", err.Error()))
		return
	}
	fmt.Println(imageURL)
	// 使用图片 URL 调用火山引擎识别
	result, err := h.volcEngineService.IdentifyFishByURL(imageURL)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Fish identification failed", "AI_SERVICE_ERROR", err.Error()))
		return
	}

	if result.Confidence < 0.1 {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Unable to identify fish in image", "LOW_CONFIDENCE",
			"The image may not contain a recognizable fish or the quality is too low"))
		return
	}

	response := map[string]interface{}{
		"species_id":      result.SpeciesID,
		"species_name":    result.SpeciesName,
		"english_name":    result.EnglishName,
		"scientific_name": result.ScientificName,
		"confidence":      result.Confidence,
		"description":     result.Description,
		"habitat":         result.Habitat,
		"processing_time": result.ProcessingTime,
		"filename":        savedKey,
		"original_name":   file.Filename,
		"file_size":       file.Size,
		"image_url":       imageURL,
		"processed_at":    time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Fish identification completed successfully", response))
}

// RemoveBackground 背景移除接口
func (h *AIHandler) RemoveBackground(c *gin.Context) {
	// 检查文件大小
	if c.Request.ContentLength > configs.AppConfig.Upload.MaxUploadSize {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("File too large", "FILE_TOO_LARGE",
			fmt.Sprintf("Maximum file size is %d bytes", configs.AppConfig.Upload.MaxUploadSize)))
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("No file uploaded", "NO_FILE", err.Error()))
		return
	}

	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	isValidExt := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			isValidExt = true
			break
		}
	}

	if !isValidExt {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid file type", "INVALID_FILE_TYPE",
			"Only JPG, PNG, GIF, and WebP files are allowed"))
		return
	}

	// 打开文件
	fileContent, err := file.Open()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to read file", "FILE_READ_ERROR", err.Error()))
		return
	}
	defer fileContent.Close()

	// 创建multipart请求
	var requestBody bytes.Buffer
	writer := multipart.NewWriter(&requestBody)

	// 添加文件字段
	fileWriter, err := writer.CreateFormFile("image", file.Filename)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to create request", "REQUEST_ERROR", err.Error()))
		return
	}

	// 复制文件内容
	_, err = io.Copy(fileWriter, fileContent)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to copy file", "COPY_ERROR", err.Error()))
		return
	}

	writer.Close()

	// 发送请求到背景移除服务
	req, err := http.NewRequest("POST", h.fishialURL+"/remove-background", &requestBody)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to create request", "REQUEST_ERROR", err.Error()))
		return
	}

	req.Header.Set("Content-Type", writer.FormDataContentType())

	client := &http.Client{
		Timeout: 30 * time.Second,
	}

	resp, err := client.Do(req)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Background removal service unavailable", "SERVICE_UNAVAILABLE", err.Error()))
		return
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to read response", "RESPONSE_ERROR", err.Error()))
		return
	}

	// 解析响应
	var bgResp BackgroundRemovalResponse
	if err := json.Unmarshal(responseBody, &bgResp); err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to parse response", "PARSE_ERROR", err.Error()))
		return
	}

	// 检查处理是否成功
	if !bgResp.Success {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Background removal failed", "REMOVAL_FAILED", bgResp.Message))
		return
	}

	// 构建响应
	response := map[string]interface{}{
		"original_filename":  bgResp.Data.OriginalFilename,
		"processed_filename": bgResp.Data.ProcessedFilename,
		"original_url":       bgResp.Data.OriginalURL,
		"processed_url":      bgResp.Data.ProcessedURL,
		"processing_time":    bgResp.Data.ProcessingTime,
		"processed_at":       bgResp.Data.ProcessedAt,
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Background removal completed successfully", response))
}
