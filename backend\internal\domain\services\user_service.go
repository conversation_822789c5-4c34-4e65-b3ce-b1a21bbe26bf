package services

import (
	"errors"
	"time"

	"fishing-api/configs"
	"fishing-api/internal/app/dto"
	"fishing-api/internal/domain/entities"
	"fishing-api/internal/infrastructure/database"

	"github.com/golang-jwt/jwt/v5"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type UserService struct {
	db *gorm.DB
}

func NewUserService() *UserService {
	return &UserService{
		db: database.GetDB(),
	}
}

// Register 用户注册
func (s *UserService) Register(req *dto.UserRegisterRequest) (*dto.UserResponse, error) {
	// 检查邮箱是否已存在
	var existingUser entities.User
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("email already exists")
	}

	// 检查用户名是否已存在
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("username already exists")
	}

	// 加密密码
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, err
	}

	// 创建用户
	user := entities.User{
		Email:    req.Email,
		Username: req.Username,
		Password: string(hashedPassword),
		Nickname: req.Nickname,
		IsActive: true,
	}

	if err := s.db.Create(&user).Error; err != nil {
		return nil, err
	}

	return s.entityToResponse(&user), nil
}

// Login 用户登录
func (s *UserService) Login(req *dto.UserLoginRequest) (*dto.UserLoginResponse, error) {
	// 查找用户
	var user entities.User
	if err := s.db.Where("email = ?", req.Email).First(&user).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("invalid email or password")
		}
		return nil, err
	}

	// 检查用户是否激活
	if !user.IsActive {
		return nil, errors.New("user account is disabled")
	}

	// 验证密码
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(req.Password)); err != nil {
		return nil, errors.New("invalid email or password")
	}

	// 更新最后登录时间
	now := time.Now()
	user.LastLoginAt = &now
	s.db.Save(&user)

	// 生成JWT token
	token, err := s.generateToken(user.ID)
	if err != nil {
		return nil, err
	}

	return &dto.UserLoginResponse{
		Token: token,
		User:  *s.entityToResponse(&user),
	}, nil
}

// GetProfile 获取用户资料
func (s *UserService) GetProfile(userID uint) (*dto.UserResponse, error) {
	var user entities.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	return s.entityToResponse(&user), nil
}

// UpdateProfile 更新用户资料
func (s *UserService) UpdateProfile(userID uint, req *dto.UpdateProfileRequest) (*dto.UserResponse, error) {
	var user entities.User
	if err := s.db.First(&user, userID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errors.New("user not found")
		}
		return nil, err
	}

	// 更新字段（只更新非空字段）
	if req.Nickname != nil {
		user.Nickname = *req.Nickname
	}
	if req.Avatar != nil {
		user.Avatar = req.Avatar
	}
	if req.Phone != nil {
		user.Phone = req.Phone
	}
	if req.Gender != nil {
		user.Gender = req.Gender
	}
	if req.Birthday != nil {
		user.Birthday = req.Birthday
	}
	if req.Province != nil {
		user.Province = req.Province
	}
	if req.City != nil {
		user.City = req.City
	}
	if req.Bio != nil {
		user.Bio = req.Bio
	}

	if err := s.db.Save(&user).Error; err != nil {
		return nil, err
	}

	return s.entityToResponse(&user), nil
}

// generateToken 生成JWT token
func (s *UserService) generateToken(userID uint) (string, error) {
	claims := jwt.MapClaims{
		"user_id": userID,
		"exp":     time.Now().Add(time.Duration(configs.AppConfig.JWT.ExpireHours) * time.Hour).Unix(),
		"iat":     time.Now().Unix(),
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(configs.AppConfig.JWT.Secret))
}

// entityToResponse 转换实体到响应DTO
func (s *UserService) entityToResponse(user *entities.User) *dto.UserResponse {
	return &dto.UserResponse{
		ID:              user.ID,
		Email:           user.Email,
		Username:        user.Username,
		Nickname:        user.Nickname,
		Avatar:          user.Avatar,
		Phone:           user.Phone,
		Gender:          user.Gender,
		Birthday:        user.Birthday,
		Province:        user.Province,
		City:            user.City,
		Bio:             user.Bio,
		IsActive:        user.IsActive,
		LastLoginAt:     user.LastLoginAt,
		TotalCatches:    user.TotalCatches,
		UnlockedSpecies: user.UnlockedSpecies,
		TotalWeight:     user.TotalWeight,
		MaxWeight:       user.MaxWeight,
		CreatedAt:       user.CreatedAt,
	}
}