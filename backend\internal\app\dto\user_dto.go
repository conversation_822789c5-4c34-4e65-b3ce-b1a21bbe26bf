package dto

import "time"

// UserRegisterRequest 用户注册请求
type UserRegisterRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Username string `json:"username" validate:"required,min=3,max=20"`
	Password string `json:"password" validate:"required,min=6"`
	Nickname string `json:"nickname" validate:"required,min=1,max=50"`
}

// UserLoginRequest 用户登录请求
type UserLoginRequest struct {
	Email    string `json:"email" validate:"required,email"`
	Password string `json:"password" validate:"required"`
}

// UserLoginResponse 用户登录响应
type UserLoginResponse struct {
	Token string       `json:"token"`
	User  UserResponse `json:"user"`
}

// UserResponse 用户信息响应
type UserResponse struct {
	ID              uint       `json:"id"`
	Email           string     `json:"email"`
	Username        string     `json:"username"`
	Nickname        string     `json:"nickname"`
	Avatar          *string    `json:"avatar"`
	Phone           *string    `json:"phone"`
	Gender          *string    `json:"gender"`
	Birthday        *time.Time `json:"birthday"`
	Province        *string    `json:"province"`
	City            *string    `json:"city"`
	Bio             *string    `json:"bio"`
	IsActive        bool       `json:"is_active"`
	LastLoginAt     *time.Time `json:"last_login_at"`
	TotalCatches    int        `json:"total_catches"`
	UnlockedSpecies int        `json:"unlocked_species"`
	TotalWeight     float64    `json:"total_weight"`
	MaxWeight       float64    `json:"max_weight"`
	CreatedAt       time.Time  `json:"created_at"`
}

// UpdateProfileRequest 更新个人资料请求
type UpdateProfileRequest struct {
	Nickname *string    `json:"nickname,omitempty" validate:"omitempty,min=1,max=50"`
	Avatar   *string    `json:"avatar,omitempty"`
	Phone    *string    `json:"phone,omitempty"`
	Gender   *string    `json:"gender,omitempty"`
	Birthday *time.Time `json:"birthday,omitempty"`
	Province *string    `json:"province,omitempty"`
	City     *string    `json:"city,omitempty"`
	Bio      *string    `json:"bio,omitempty"`
}