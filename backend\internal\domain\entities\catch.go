package entities

import (
	"time"
)

// Catch 渔获记录模型
type Catch struct {
	BaseModel
	UserID        uint    `gorm:"not null;index" json:"user_id"`
	FishSpeciesID uint    `gorm:"not null;index" json:"fish_species_id"`
	
	// 基础信息
	Weight        *float64  `json:"weight"`         // 重量(kg)
	Length        *float64  `json:"length"`         // 长度(cm)
	CatchTime     *time.Time `gorm:"not null" json:"catch_time"`
	
	// 位置信息
	Latitude      *float64  `json:"latitude"`
	Longitude     *float64  `json:"longitude"`
	LocationName  *string   `gorm:"type:varchar(255)" json:"location_name"` // 地点名称
	
	// 天气信息
	Weather       *string   `gorm:"type:varchar(100)" json:"weather"`      // 天气状况
	Temperature   *float64  `json:"temperature"`    // 温度(°C)
	
	// 钓具信息
	BaitUsed      *string   `gorm:"type:varchar(100)" json:"bait_used"`     // 鱼饵
	TechniqueUsed *string   `gorm:"type:varchar(100)" json:"technique_used"` // 钓法
	
	// 图片信息
	PhotoURL         *string `gorm:"type:text" json:"photo_url"`          // 原始照片URL
	ProcessedPhotoURL *string `gorm:"type:text" json:"processed_photo_url"` // AI处理后照片URL
	
	// 其他信息
	Notes        *string `gorm:"type:text" json:"notes"`         // 备注
	IsPublic     bool    `gorm:"default:true" json:"is_public"`  // 是否公开
	IsVerified   bool    `gorm:"default:false" json:"is_verified"` // 是否已验证
	
	// AI处理信息
	AIProcessed  bool     `gorm:"default:false" json:"ai_processed"`
	AIConfidence *float64 `json:"ai_confidence"`    // AI识别置信度
	
	// 关联关系
	User        User        `gorm:"foreignKey:UserID" json:"user,omitempty"`
	FishSpecies FishSpecies `gorm:"foreignKey:FishSpeciesID" json:"fish_species,omitempty"`
}

// TableName 指定表名
func (Catch) TableName() string {
	return "catches"
}

