package entities

import "time"

// FishSpecies 鱼种模型
type FishSpecies struct {
	BaseModel
	Name         string  `gorm:"uniqueIndex;not null" json:"name" validate:"required"`
	EnglishName  *string `json:"english_name"`
	ScientificName *string `json:"scientific_name"`
	Family       *string `json:"family"`
	Description  *string `gorm:"type:text" json:"description"`
	Habitat      *string `gorm:"type:text" json:"habitat"`
	Distribution *string `gorm:"type:text" json:"distribution"`
	
	// 外观特征
	Color        *string `gorm:"type:text" json:"color"`
	Shape        *string `gorm:"type:text" json:"shape"`
	MaxLength    *float64 `json:"max_length"`    // 最大长度(cm)
	MaxWeight    *float64 `json:"max_weight"`    // 最大重量(kg)
	
	// 图片信息
	ImageURL     *string `json:"image_url"`
	ThumbnailURL *string `json:"thumbnail_url"`
	
	// 稀有度和分类
	Rarity       string `gorm:"default:'common'" json:"rarity"` // common, uncommon, rare, legendary
	Category     string `gorm:"default:'freshwater'" json:"category"` // freshwater, saltwater, brackish
	
	// 是否激活
	IsActive     bool   `gorm:"default:true" json:"is_active"`
	
	// 关联关系
	Catches      []Catch    `gorm:"foreignKey:FishSpeciesID" json:"-"`
	UserFishdex  []UserFish `gorm:"foreignKey:SpeciesID" json:"-"`
}

// TableName 指定表名
func (FishSpecies) TableName() string {
	return "fish_species"
}

// UserFish 用户鱼种图鉴（用户解锁的鱼种）
type UserFish struct {
	BaseModel
	UserID      uint         `gorm:"not null;index:idx_user_species,unique" json:"user_id"`
	SpeciesID   uint         `gorm:"not null;index:idx_user_species,unique" json:"species_id"`
	FirstCatchID uint        `gorm:"not null" json:"first_catch_id"` // 第一次捕获的记录ID
	TotalCatches int         `gorm:"default:1" json:"total_catches"`
	MaxWeight   *float64     `json:"max_weight"`
	MaxLength   *float64     `json:"max_length"`
	UnlockedAt  time.Time    `json:"unlocked_at"`
	
	// 关联关系
	User        User         `gorm:"foreignKey:UserID" json:"user,omitempty"`
	Species     FishSpecies  `gorm:"foreignKey:SpeciesID" json:"species,omitempty"`
	FirstCatch  Catch        `gorm:"foreignKey:FirstCatchID" json:"first_catch,omitempty"`
}

// TableName 指定表名
func (UserFish) TableName() string {
	return "user_fishdex"
}