package handlers

import (
	"net/http"
	"strconv"

	"fishing-api/internal/app/dto"
	"fishing-api/internal/domain/services"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type UserHandler struct {
	userService *services.UserService
	validator   *validator.Validate
}

func NewUserHandler() *UserHandler {
	return &UserHandler{
		userService: services.NewUserService(),
		validator:   validator.New(),
	}
}

// Register 用户注册
func (h *UserHandler) Register(c *gin.Context) {
	var req dto.UserRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid request body", "INVALID_REQUEST", err.Error()))
		return
	}

	// 验证请求数据
	if err := h.validator.Struct(&req); err != nil {
		c.<PERSON>(http.StatusBadRequest, dto.ValidationErrorResponse(err.Error()))
		return
	}

	// 调用服务层
	user, err := h.userService.Register(&req)
	if err != nil {
		if err.Error() == "email already exists" || err.Error() == "username already exists" {
			c.JSON(http.StatusConflict, dto.ErrorResponse(err.Error(), "CONFLICT", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Registration failed", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusCreated, dto.SuccessResponse("User registered successfully", user))
}

// Login 用户登录
func (h *UserHandler) Login(c *gin.Context) {
	var req dto.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid request body", "INVALID_REQUEST", err.Error()))
		return
	}

	// 验证请求数据
	if err := h.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ValidationErrorResponse(err.Error()))
		return
	}

	// 调用服务层
	response, err := h.userService.Login(&req)
	if err != nil {
		if err.Error() == "invalid email or password" {
			c.JSON(http.StatusUnauthorized, dto.ErrorResponse(err.Error(), "INVALID_CREDENTIALS", ""))
			return
		}
		if err.Error() == "user account is disabled" {
			c.JSON(http.StatusForbidden, dto.ErrorResponse(err.Error(), "ACCOUNT_DISABLED", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Login failed", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Login successful", response))
}

// GetProfile 获取用户资料
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	profile, err := h.userService.GetProfile(userID.(uint))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse(err.Error(), "NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get profile", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Profile retrieved successfully", profile))
}

// UpdateProfile 更新用户资料
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	var req dto.UpdateProfileRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid request body", "INVALID_REQUEST", err.Error()))
		return
	}

	// 验证请求数据
	if err := h.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ValidationErrorResponse(err.Error()))
		return
	}

	// 调用服务层
	profile, err := h.userService.UpdateProfile(userID.(uint), &req)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse(err.Error(), "NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to update profile", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Profile updated successfully", profile))
}

// GetPublicProfile 获取用户公开资料
func (h *UserHandler) GetPublicProfile(c *gin.Context) {
	userIDStr := c.Param("id")
	userID, err := strconv.ParseUint(userIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid user ID", "INVALID_REQUEST", ""))
		return
	}

	profile, err := h.userService.GetProfile(uint(userID))
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse(err.Error(), "NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get profile", "INTERNAL_ERROR", err.Error()))
		return
	}

	// 返回公开信息，隐藏敏感字段
	publicProfile := &dto.UserResponse{
		ID:              profile.ID,
		Username:        profile.Username,
		Nickname:        profile.Nickname,
		Avatar:          profile.Avatar,
		Gender:          profile.Gender,
		Province:        profile.Province,
		City:            profile.City,
		Bio:             profile.Bio,
		TotalCatches:    profile.TotalCatches,
		UnlockedSpecies: profile.UnlockedSpecies,
		TotalWeight:     profile.TotalWeight,
		MaxWeight:       profile.MaxWeight,
		CreatedAt:       profile.CreatedAt,
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Public profile retrieved successfully", publicProfile))
}