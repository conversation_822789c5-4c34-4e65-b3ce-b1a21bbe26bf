package handlers

import (
	"net/http"
	"strconv"

	"fishing-api/internal/app/dto"
	"fishing-api/internal/domain/services"

	"github.com/gin-gonic/gin"
)

type LeaderboardHandler struct {
	leaderboardService *services.LeaderboardService
}

func NewLeaderboardHandler() *LeaderboardHandler {
	return &LeaderboardHandler{
		leaderboardService: services.NewLeaderboardService(),
	}
}

// GetRegionalLeaderboard 获取区域排行榜
func (h *LeaderboardHandler) GetRegionalLeaderboard(c *gin.Context) {
	// 解析查询参数
	province := c.Query("province")
	city := c.Query("city")
	period := c.Default<PERSON>uery("period", "monthly") // weekly, monthly, yearly
	limit, _ := strconv.Atoi(c.Default<PERSON>uer<PERSON>("limit", "20"))

	leaderboard, err := h.leaderboardService.GetRegionalLeaderboard(province, city, period, limit)
	if err != nil {
		c.<PERSON>(http.StatusInternalServerError, dto.ErrorResponse("Failed to get regional leaderboard", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.<PERSON>(http.StatusOK, dto.SuccessResponse("Regional leaderboard retrieved successfully", leaderboard))
}

// GetTopFishers 获取顶级钓手排行
func (h *LeaderboardHandler) GetTopFishers(c *gin.Context) {
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	topFishers, err := h.leaderboardService.GetTopFishers(limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get top fishers", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Top fishers retrieved successfully", topFishers))
}

// GetSpeciesLeaderboard 获取鱼种排行榜
func (h *LeaderboardHandler) GetSpeciesLeaderboard(c *gin.Context) {
	speciesIDStr := c.Param("id")
	speciesID, err := strconv.ParseUint(speciesIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid species ID", "INVALID_REQUEST", ""))
		return
	}

	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	leaderboard, err := h.leaderboardService.GetSpeciesLeaderboard(uint(speciesID), limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get species leaderboard", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Species leaderboard retrieved successfully", leaderboard))
}

// GetUserRank 获取用户排名
func (h *LeaderboardHandler) GetUserRank(c *gin.Context) {
	// 解析查询参数
	var targetUserID uint
	
	// 检查是否查询特定用户
	if userIDStr := c.Query("user_id"); userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			targetUserID = uint(uid)
		} else {
			c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid user ID", "INVALID_REQUEST", ""))
			return
		}
	} else {
		// 默认查询当前登录用户
		userID, exists := c.Get("user_id")
		if !exists {
			c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
			return
		}
		targetUserID = userID.(uint)
	}

	province := c.Query("province")
	city := c.Query("city")
	period := c.DefaultQuery("period", "monthly")
	metric := c.DefaultQuery("metric", "total_weight") // total_weight, total_catches, unlocked_species

	userRank, err := h.leaderboardService.GetUserRank(targetUserID, province, city, period, metric)
	if err != nil {
		if err.Error() == "user not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse("User not found", "NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get user rank", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("User rank retrieved successfully", userRank))
}