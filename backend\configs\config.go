package configs

import (
	"fmt"
	"log"
	"os"
	"strconv"
	"time"

	"github.com/joho/godotenv"
)

type Config struct {
	Server     ServerConfig
	Database   DatabaseConfig
	JWT        JWTConfig
	Upload     UploadConfig
	AI         AIConfig
	VolcEngine VolcEngineConfig
	TOS        TOSConfig
	Weather    WeatherConfig
	Map        MapConfig
	Log        LogConfig
	Redis      RedisConfig
}

type ServerConfig struct {
	Host string
	Port string
	Mode string
}

type DatabaseConfig struct {
	Host     string
	Port     string
	Name     string
	User     string
	Password string
	SSLMode  string
	TimeZone string
}

type JWTConfig struct {
	Secret      string
	ExpireHours time.Duration
}

type UploadConfig struct {
	UploadPath    string
	MaxUploadSize int64
}

type AIConfig struct {
	FishialURL  string
	FishialKey  string
	ClipdropURL string
	ClipdropKey string
}

type WeatherConfig struct {
	URL string
	Key string
}

type MapConfig struct {
	GaodeKey string
}

type LogConfig struct {
	Level      string
	FilePath   string
	MaxSize    int
	MaxBackups int
	MaxAge     int
}

type VolcEngineConfig struct {
	ApiKey  string
	ModelID string
}

// TOSConfig 火山引擎对象存储配置
type TOSConfig struct {
	AccessKey string
	SecretKey string
	Endpoint  string
	Region    string
	Bucket    string
	BaseURL   string // 自定义访问域名（如 CDN/绑定域名），用于拼接公网 URL
	ACL       string // public-read 或 private，默认 private
	Prefix    string // 对象键前缀，如 uploads/
}

type RedisConfig struct {
	Host     string
	Port     string
	Password string
	DB       int
}

var AppConfig *Config

// LoadConfig 加载配置
func LoadConfig() error {
	// 加载环境变量文件：尝试多级目录，支持项目根目录放置 .env
	if err := godotenv.Load(".env"); err != nil {
		if err2 := godotenv.Load("../.env"); err2 != nil {
			if err3 := godotenv.Load("../../.env"); err3 != nil {
				log.Println("Warning: .env file not found, using environment variables")
			}
		}
	}

	AppConfig = &Config{
		Server: ServerConfig{
			Host: getEnv("SERVER_HOST", "0.0.0.0"),
			Port: getEnv("SERVER_PORT", "8030"),
			Mode: getEnv("GIN_MODE", "debug"),
		},
		Database: DatabaseConfig{
			Host:     getEnv("DB_HOST", "localhost"),
			Port:     getEnv("DB_PORT", "5432"),
			Name:     getEnv("DB_NAME", "fishing_app"),
			User:     getEnv("DB_USER", "postgres"),
			Password: getEnv("DB_PASSWORD", ""),
			SSLMode:  getEnv("DB_SSLMODE", "disable"),
			TimeZone: getEnv("DB_TIMEZONE", "Asia/Shanghai"),
		},
		JWT: JWTConfig{
			Secret:      getEnv("JWT_SECRET", "default-secret-key"),
			ExpireHours: time.Duration(getEnvAsInt("JWT_EXPIRE_HOURS", 24)) * time.Hour,
		},
		Upload: UploadConfig{
			UploadPath:    getEnv("UPLOAD_PATH", "./uploads"),
			MaxUploadSize: int64(getEnvAsInt("MAX_UPLOAD_SIZE", 10485760)),
		},
		AI: AIConfig{
			FishialURL:  getEnv("FISHIAL_API_URL", ""),
			FishialKey:  getEnv("FISHIAL_API_KEY", ""),
			ClipdropURL: getEnv("CLIPDROP_API_URL", "https://clipdrop-api.co"),
			ClipdropKey: getEnv("CLIPDROP_API_KEY", ""),
		},
		VolcEngine: VolcEngineConfig{
			ApiKey:  getEnv("VOLCENGINE_API_KEY", ""),
			ModelID: getEnv("VOLCENGINE_MODEL_ID", "ep-xxxxxx-yyy"),
		},
		TOS: TOSConfig{
			AccessKey: getEnv("TOS_ACCESS_KEY", ""),
			SecretKey: getEnv("TOS_SECRET_KEY", ""),
			Endpoint:  getEnv("TOS_ENDPOINT", "https://tos-cn-beijing.volces.com"),
			Region:    getEnv("TOS_REGION", "cn-beijing"),
			Bucket:    getEnv("TOS_BUCKET", ""),
			BaseURL:   getEnv("TOS_BASE_URL", ""),
			ACL:       getEnv("TOS_ACL", "private"),
			Prefix:    getEnv("TOS_PREFIX", "uploads/"),
		},
		Weather: WeatherConfig{
			URL: getEnv("WEATHER_API_URL", "https://devapi.qweather.com/v7"),
			Key: getEnv("WEATHER_API_KEY", ""),
		},
		Map: MapConfig{
			GaodeKey: getEnv("GAODE_API_KEY", ""),
		},
		Log: LogConfig{
			Level:      getEnv("LOG_LEVEL", "debug"),
			FilePath:   getEnv("LOG_FILE_PATH", "./logs/app.log"),
			MaxSize:    getEnvAsInt("LOG_MAX_SIZE", 100),
			MaxBackups: getEnvAsInt("LOG_MAX_BACKUPS", 3),
			MaxAge:     getEnvAsInt("LOG_MAX_AGE", 28),
		},
		Redis: RedisConfig{
			Host:     getEnv("REDIS_HOST", "localhost"),
			Port:     getEnv("REDIS_PORT", "6379"),
			Password: getEnv("REDIS_PASSWORD", ""),
			DB:       getEnvAsInt("REDIS_DB", 0),
		},
	}

	return nil
}

// GetDSN 获取数据库连接字符串
func (c *Config) GetDSN() string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=%s TimeZone=%s",
		c.Database.Host,
		c.Database.User,
		c.Database.Password,
		c.Database.Name,
		c.Database.Port,
		c.Database.SSLMode,
		c.Database.TimeZone,
	)
}

// GetServerAddr 获取服务器地址
func (c *Config) GetServerAddr() string {
	return fmt.Sprintf("%s:%s", c.Server.Host, c.Server.Port)
}

// getEnv 获取环境变量，如果不存在则返回默认值
func getEnv(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// getEnvAsInt 获取环境变量并转换为整数
func getEnvAsInt(key string, defaultValue int) int {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}

	intValue, err := strconv.Atoi(value)
	if err != nil {
		return defaultValue
	}

	return intValue
}
