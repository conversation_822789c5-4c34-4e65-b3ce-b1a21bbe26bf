package handlers

import (
	"net/http"
	"strconv"

	"fishing-api/internal/app/dto"
	"fishing-api/internal/domain/services"

	"github.com/gin-gonic/gin"
)

type FishdexHandler struct {
	fishdexService *services.FishdexService
}

func NewFishdexHandler() *FishdexHandler {
	return &FishdexHandler{
		fishdexService: services.NewFishdexService(),
	}
}

// GetUserFishdex 获取用户鱼种图鉴
func (h *FishdexHandler) GetUserFishdex(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.<PERSON>("page_size", "20"))

	fishdex, err := h.fishdexService.GetUserFishdex(userID.(uint), page, pageSize)
	if err != nil {
		c.J<PERSON>(http.StatusInternalServerError, dto.ErrorResponse("Failed to get user fishdex", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("User fishdex retrieved successfully", fishdex))
}

// GetAllSpecies 获取所有鱼种
func (h *FishdexHandler) GetAllSpecies(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	category := c.Query("category")

	species, err := h.fishdexService.GetAllSpecies(page, pageSize, category)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get species list", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Species list retrieved successfully", species))
}

// GetSpeciesDetail 获取鱼种详情
func (h *FishdexHandler) GetSpeciesDetail(c *gin.Context) {
	speciesIDStr := c.Param("id")
	speciesID, err := strconv.ParseUint(speciesIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid species ID", "INVALID_REQUEST", ""))
		return
	}

	// 获取当前用户ID（如果已登录）
	var userID *uint
	if uid, exists := c.Get("user_id"); exists {
		uidUint := uid.(uint)
		userID = &uidUint
	}

	detail, err := h.fishdexService.GetSpeciesDetail(uint(speciesID), userID)
	if err != nil {
		if err.Error() == "species not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse("Species not found", "NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get species detail", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Species detail retrieved successfully", detail))
}

// GetCategories 获取鱼种分类列表
func (h *FishdexHandler) GetCategories(c *gin.Context) {
	categories, err := h.fishdexService.GetCategories()
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get categories", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Categories retrieved successfully", categories))
}

// SearchSpecies 搜索鱼种
func (h *FishdexHandler) SearchSpecies(c *gin.Context) {
	keyword := c.Query("keyword")
	if keyword == "" {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Keyword is required", "INVALID_REQUEST", ""))
		return
	}

	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	category := c.Query("category")

	// 获取当前用户ID（如果已登录）
	var userID *uint
	if uid, exists := c.Get("user_id"); exists {
		uidUint := uid.(uint)
		userID = &uidUint
	}

	species, err := h.fishdexService.SearchSpecies(keyword, category, userID, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to search species", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Species search completed successfully", species))
}

// GetRecommendedSpecies 获取推荐鱼种
func (h *FishdexHandler) GetRecommendedSpecies(c *gin.Context) {
	// 解析查询参数
	var lat, lng *float64
	if latStr := c.Query("lat"); latStr != "" {
		if latVal, err := strconv.ParseFloat(latStr, 64); err == nil {
			lat = &latVal
		}
	}
	if lngStr := c.Query("lng"); lngStr != "" {
		if lngVal, err := strconv.ParseFloat(lngStr, 64); err == nil {
			lng = &lngVal
		}
	}

	province := c.Query("province")
	city := c.Query("city")
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	species, err := h.fishdexService.GetRecommendedSpecies(userID.(uint), lat, lng, province, city, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get recommended species", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Recommended species retrieved successfully", species))
}