package handlers

import (
	"fmt"
	"net/http"
	"path/filepath"
	"strings"
	"time"

	"fishing-api/configs"
	"fishing-api/internal/app/dto"
	"fishing-api/internal/infrastructure/external"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

type UploadHandler struct{}

func NewUploadHandler() *UploadHandler {
	return &UploadHandler{}
}

// UploadImage 上传图片
func (h *UploadHandler) UploadImage(c *gin.Context) {
	// 检查文件大小
	if c.Request.ContentLength > configs.AppConfig.Upload.MaxUploadSize {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("File too large", "FILE_TOO_LARGE",
			fmt.Sprintf("Maximum file size is %d bytes", configs.AppConfig.Upload.MaxUploadSize)))
		return
	}

	// 获取上传的文件
	file, err := c.FormFile("image")
	if err != nil {
		c.J<PERSON>N(http.StatusBadRequest, dto.ErrorResponse("No file uploaded", "NO_FILE", err.Error()))
		return
	}

	// 检查文件类型
	ext := strings.ToLower(filepath.Ext(file.Filename))
	allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
	isValidExt := false
	for _, allowedExt := range allowedExts {
		if ext == allowedExt {
			isValidExt = true
			break
		}
	}

	if !isValidExt {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid file type", "INVALID_FILE_TYPE",
			"Only JPG, PNG, GIF, and WebP files are allowed"))
		return
	}

	// 生成唯一文件名
	fileName := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)

	// 优先上传到对象存储（如已配置）
	if configs.AppConfig != nil && configs.AppConfig.TOS.Bucket != "" && configs.AppConfig.TOS.AccessKey != "" && configs.AppConfig.TOS.SecretKey != "" {
		// 打开文件读取流
		src, err := file.Open()
		if err != nil {
			c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to open file", "OPEN_ERROR", err.Error()))
			return
		}
		defer src.Close()

		objectKey := external.BuildObjectKey(fileName)
		url, key, err := external.UploadImage(c.Request.Context(), objectKey, src)
		if err != nil {
			c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to upload to object storage", "TOS_UPLOAD_ERROR", err.Error()))
			return
		}

		response := map[string]interface{}{
			"filename":      key,
			"original_name": file.Filename,
			"size":          file.Size,
			"url":           url,
			"uploaded_at":   time.Now(),
		}
		c.JSON(http.StatusOK, dto.SuccessResponse("Image uploaded successfully", response))
		return
	}

	// 否则回落到本地保存
	uploadPath := configs.AppConfig.Upload.UploadPath
	filePath := filepath.Join(uploadPath, fileName)
	if err := c.SaveUploadedFile(file, filePath); err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to save file", "SAVE_ERROR", err.Error()))
		return
	}
	fileURL := fmt.Sprintf("/uploads/%s", fileName)
	response := map[string]interface{}{
		"filename":      fileName,
		"original_name": file.Filename,
		"size":          file.Size,
		"url":           fileURL,
		"uploaded_at":   time.Now(),
	}
	c.JSON(http.StatusOK, dto.SuccessResponse("Image uploaded successfully", response))
}

// UploadMultipleImages 批量上传图片
func (h *UploadHandler) UploadMultipleImages(c *gin.Context) {
	form, err := c.MultipartForm()
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Failed to parse form", "FORM_ERROR", err.Error()))
		return
	}

	files := form.File["images"]
	if len(files) == 0 {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("No files uploaded", "NO_FILES", ""))
		return
	}

	if len(files) > 10 { // 限制最多10个文件
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Too many files", "TOO_MANY_FILES", "Maximum 10 files allowed"))
		return
	}

	var results []map[string]interface{}
	var errors []string

	useTOS := configs.AppConfig != nil && configs.AppConfig.TOS.Bucket != "" && configs.AppConfig.TOS.AccessKey != "" && configs.AppConfig.TOS.SecretKey != ""

	for _, file := range files {
		// 检查文件类型
		ext := strings.ToLower(filepath.Ext(file.Filename))
		allowedExts := []string{".jpg", ".jpeg", ".png", ".gif", ".webp"}
		isValidExt := false
		for _, allowedExt := range allowedExts {
			if ext == allowedExt {
				isValidExt = true
				break
			}
		}

		if !isValidExt {
			errors = append(errors, fmt.Sprintf("Invalid file type for %s", file.Filename))
			continue
		}

		// 检查文件大小
		if file.Size > configs.AppConfig.Upload.MaxUploadSize {
			errors = append(errors, fmt.Sprintf("File %s is too large", file.Filename))
			continue
		}

		// 生成唯一文件名
		fileName := fmt.Sprintf("%s_%d%s", uuid.New().String(), time.Now().Unix(), ext)

		if useTOS {
			src, err := file.Open()
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to open %s: %s", file.Filename, err.Error()))
				continue
			}
			objectKey := external.BuildObjectKey(fileName)
			url, key, err := external.UploadImage(c.Request.Context(), objectKey, src)
			src.Close()
			if err != nil {
				errors = append(errors, fmt.Sprintf("Failed to upload %s: %s", file.Filename, err.Error()))
				continue
			}
			results = append(results, map[string]interface{}{
				"filename":      key,
				"original_name": file.Filename,
				"size":          file.Size,
				"url":           url,
				"uploaded_at":   time.Now(),
			})
			continue
		}

		// 本地保存
		uploadPath := configs.AppConfig.Upload.UploadPath
		filePath := filepath.Join(uploadPath, fileName)
		if err := c.SaveUploadedFile(file, filePath); err != nil {
			errors = append(errors, fmt.Sprintf("Failed to save %s: %s", file.Filename, err.Error()))
			continue
		}
		fileURL := fmt.Sprintf("/uploads/%s", fileName)
		results = append(results, map[string]interface{}{
			"filename":      fileName,
			"original_name": file.Filename,
			"size":          file.Size,
			"url":           fileURL,
			"uploaded_at":   time.Now(),
		})
	}

	response := map[string]interface{}{
		"success_count": len(results),
		"error_count":   len(errors),
		"files":         results,
		"errors":        errors,
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Batch upload completed", response))
}
