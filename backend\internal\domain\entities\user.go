package entities

import (
	"time"

	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	BaseModel
	Email       string     `gorm:"uniqueIndex;not null" json:"email" validate:"required,email"`
	Username    string     `gorm:"uniqueIndex;not null" json:"username" validate:"required,min=3,max=20"`
	Password    string     `gorm:"not null" json:"-" validate:"required,min=6"`
	Nickname    string     `gorm:"not null" json:"nickname" validate:"required,min=1,max=50"`
	Avatar      *string    `json:"avatar"`
	Phone       *string    `gorm:"uniqueIndex" json:"phone"`
	Gender      *string    `gorm:"type:varchar(10)" json:"gender"`
	Birthday    *time.Time `json:"birthday"`
	Province    *string    `gorm:"type:varchar(50)" json:"province"`
	City        *string    `gorm:"type:varchar(50)" json:"city"`
	Bio         *string    `gorm:"type:text" json:"bio"`
	IsActive    bool       `gorm:"default:true" json:"is_active"`
	LastLoginAt *time.Time `json:"last_login_at"`

	// 统计信息
	TotalCatches    int     `gorm:"default:0" json:"total_catches"`
	UnlockedSpecies int     `gorm:"default:0" json:"unlocked_species"`
	TotalWeight     float64 `gorm:"default:0" json:"total_weight"`
	MaxWeight       float64 `gorm:"default:0" json:"max_weight"`

	// 关联关系
	Catches      []Catch      `gorm:"foreignKey:UserID" json:"-"`
	UserFishdex  []UserFish   `gorm:"foreignKey:UserID" json:"-"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// HashPassword 加密密码
func (u *User) HashPassword(password string) error {
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return err
	}
	u.Password = string(hashedBytes)
	return nil
}

// CheckPassword 检查密码
func (u *User) CheckPassword(password string) bool {
	err := bcrypt.CompareHashAndPassword([]byte(u.Password), []byte(password))
	return err == nil
}

// BeforeCreate 创建前钩子
func (u *User) BeforeCreate(tx *gorm.DB) error {
	// 如果没有设置昵称，使用用户名
	if u.Nickname == "" {
		u.Nickname = u.Username
	}
	return nil
}