package handlers

import (
	"net/http"
	"strconv"

	"fishing-api/internal/app/dto"
	"fishing-api/internal/domain/services"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
)

type CatchHandler struct {
	catchService *services.CatchService
	validator    *validator.Validate
}

func NewCatchHandler() *CatchHandler {
	return &CatchHandler{
		catchService: services.NewCatchService(),
		validator:    validator.New(),
	}
}

// CreateCatch 创建渔获记录
func (h *CatchHandler) CreateCatch(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	var req dto.CreateCatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid request body", "INVALID_REQUEST", err.Error()))
		return
	}

	// 验证请求数据
	if err := h.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ValidationErrorResponse(err.Error()))
		return
	}

	// 调用服务层
	catch, err := h.catchService.CreateCatch(userID.(uint), &req)
	if err != nil {
		if err.Error() == "fish species not found" {
			c.JSON(http.StatusBadRequest, dto.ErrorResponse(err.Error(), "FISH_SPECIES_NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to create catch", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusCreated, dto.SuccessResponse("Catch created successfully", catch))
}

// GetCatch 获取单个渔获记录
func (h *CatchHandler) GetCatch(c *gin.Context) {
	catchIDStr := c.Param("id")
	catchID, err := strconv.ParseUint(catchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid catch ID", "INVALID_REQUEST", ""))
		return
	}

	// 获取当前用户ID（如果已登录）
	var userID *uint
	if uid, exists := c.Get("user_id"); exists {
		uidUint := uid.(uint)
		userID = &uidUint
	}

	catch, err := h.catchService.GetCatch(uint(catchID), userID)
	if err != nil {
		if err.Error() == "catch not found or access denied" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse("Catch not found", "NOT_FOUND", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get catch", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Catch retrieved successfully", catch))
}

// GetCatches 获取渔获记录列表
func (h *CatchHandler) GetCatches(c *gin.Context) {
	// 解析查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))
	userIDStr := c.Query("user_id")
	onlyPublic := c.Query("only_public") == "true"

	var targetUserID *uint
	if userIDStr != "" {
		if uid, err := strconv.ParseUint(userIDStr, 10, 32); err == nil {
			uidUint := uint(uid)
			targetUserID = &uidUint
		}
	}

	// 获取当前登录用户ID
	var currentUserID *uint
	if uid, exists := c.Get("user_id"); exists {
		uidUint := uid.(uint)
		currentUserID = &uidUint
	}

	// 如果查询特定用户的记录，且是本人查询，可以看到私有记录
	if targetUserID != nil && currentUserID != nil && *targetUserID == *currentUserID {
		onlyPublic = false
	}

	catches, err := h.catchService.GetCatches(targetUserID, page, pageSize, onlyPublic)
	if err != nil {
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to get catches", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Catches retrieved successfully", catches))
}

// UpdateCatch 更新渔获记录
func (h *CatchHandler) UpdateCatch(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	catchIDStr := c.Param("id")
	catchID, err := strconv.ParseUint(catchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid catch ID", "INVALID_REQUEST", ""))
		return
	}

	var req dto.UpdateCatchRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid request body", "INVALID_REQUEST", err.Error()))
		return
	}

	// 验证请求数据
	if err := h.validator.Struct(&req); err != nil {
		c.JSON(http.StatusBadRequest, dto.ValidationErrorResponse(err.Error()))
		return
	}

	// 调用服务层
	catch, err := h.catchService.UpdateCatch(uint(catchID), userID.(uint), &req)
	if err != nil {
		if err.Error() == "catch not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse(err.Error(), "NOT_FOUND", ""))
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, dto.ErrorResponse(err.Error(), "ACCESS_DENIED", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to update catch", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Catch updated successfully", catch))
}

// DeleteCatch 删除渔获记录
func (h *CatchHandler) DeleteCatch(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, dto.ErrorResponse("User not authenticated", "UNAUTHORIZED", ""))
		return
	}

	catchIDStr := c.Param("id")
	catchID, err := strconv.ParseUint(catchIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, dto.ErrorResponse("Invalid catch ID", "INVALID_REQUEST", ""))
		return
	}

	// 调用服务层
	err = h.catchService.DeleteCatch(uint(catchID), userID.(uint))
	if err != nil {
		if err.Error() == "catch not found" {
			c.JSON(http.StatusNotFound, dto.ErrorResponse(err.Error(), "NOT_FOUND", ""))
			return
		}
		if err.Error() == "access denied" {
			c.JSON(http.StatusForbidden, dto.ErrorResponse(err.Error(), "ACCESS_DENIED", ""))
			return
		}
		c.JSON(http.StatusInternalServerError, dto.ErrorResponse("Failed to delete catch", "INTERNAL_ERROR", err.Error()))
		return
	}

	c.JSON(http.StatusOK, dto.SuccessResponse("Catch deleted successfully", nil))
}