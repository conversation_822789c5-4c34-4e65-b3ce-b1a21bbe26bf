package main

import (
	"context"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"fishing-api/configs"
	"fishing-api/internal/infrastructure/database"
	"fishing-api/internal/infrastructure/logger"
	"fishing-api/internal/interfaces/http/router"

	"github.com/gin-gonic/gin"
)

func main() {
	// 加载配置
	if err := configs.LoadConfig(); err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	if err := logger.Init(); err != nil {
		log.Fatalf("Failed to initialize logger: %v", err)
	}
	defer logger.Sync()

	logger.Info("Starting Fishing API Server...")

	// 初始化数据库
	if err := database.Init(); err != nil {
		logger.Fatal("Failed to initialize database", "error", err)
	}

	// 设置Gin模式
	gin.SetMode(configs.AppConfig.Server.Mode)

	// 创建路由
	r := router.NewRouter()

	// 创建HTTP服务器
	srv := &http.Server{
		Addr:    configs.AppConfig.GetServerAddr(),
		Handler: r,
	}

	// 在goroutine中启动服务器
	go func() {
		logger.Info("Server starting", "address", srv.Addr)
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logger.Fatal("Failed to start server", "error", err)
		}
	}()

	// 等待中断信号以优雅地关闭服务器
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logger.Info("Server shutting down...")

	// 给服务器5秒时间来完成现有请求
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := srv.Shutdown(ctx); err != nil {
		logger.Fatal("Server forced to shutdown", "error", err)
	}

	// 关闭数据库连接
	if err := database.Close(); err != nil {
		logger.Error("Failed to close database connection", "error", err)
	}

	logger.Info("Server exited")
}