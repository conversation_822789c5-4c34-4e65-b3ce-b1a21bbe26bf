package dto

import "time"

// UserFishdexResponse 用户鱼种图鉴响应
type UserFishdexResponse struct {
	UnlockedSpecies []UserFishResponse `json:"unlocked_species"`
	Total           int64              `json:"total"`
	Page            int                `json:"page"`
	PageSize        int                `json:"page_size"`
	TotalPages      int                `json:"total_pages"`
}

// UserFishResponse 用户鱼种响应
type UserFishResponse struct {
	SpeciesID     uint                `json:"species_id"`
	FirstCatchID  uint                `json:"first_catch_id"`
	TotalCatches  int                 `json:"total_catches"`
	MaxWeight     *float64            `json:"max_weight"`
	MaxLength     *float64            `json:"max_length"`
	UnlockedAt    time.Time           `json:"unlocked_at"`
	Species       FishSpeciesResponse `json:"species"`
}

// SpeciesListResponse 鱼种列表响应
type SpeciesListResponse struct {
	Data       []FishSpeciesResponse `json:"data"`
	Total      int64                 `json:"total"`
	Page       int                   `json:"page"`
	PageSize   int                   `json:"page_size"`
	TotalPages int                   `json:"total_pages"`
}

// SpeciesDetailResponse 鱼种详情响应
type SpeciesDetailResponse struct {
	Species     FishSpeciesResponse `json:"species"`
	IsUnlocked  bool                `json:"is_unlocked"`
	UserStats   *UserFishStats      `json:"user_stats,omitempty"`
	GlobalStats *FishGlobalStats    `json:"global_stats"`
}

// UserFishStats 用户鱼种统计
type UserFishStats struct {
	TotalCatches int       `json:"total_catches"`
	MaxWeight    *float64  `json:"max_weight"`
	MaxLength    *float64  `json:"max_length"`
	UnlockedAt   time.Time `json:"unlocked_at"`
}

// FishGlobalStats 鱼种全局统计
type FishGlobalStats struct {
	TotalCatches   int     `json:"total_catches"`
	TotalWeight    float64 `json:"total_weight"`
	AverageWeight  float64 `json:"average_weight"`
	MaxWeight      float64 `json:"max_weight"`
	MaxLength      float64 `json:"max_length"`
}