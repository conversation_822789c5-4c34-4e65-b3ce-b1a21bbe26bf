package services

import (
	"time"

	"fishing-api/internal/app/dto"
	"fishing-api/internal/domain/entities"
	"fishing-api/internal/infrastructure/database"

	"gorm.io/gorm"
)

type LeaderboardService struct {
	db *gorm.DB
}

func NewLeaderboardService() *LeaderboardService {
	return &LeaderboardService{
		db: database.GetDB(),
	}
}

// GetRegionalLeaderboard 获取区域排行榜
func (s *LeaderboardService) GetRegionalLeaderboard(province, city string, period string, limit int) (*dto.LeaderboardResponse, error) {
	if limit <= 0 || limit > 100 {
		limit = 20
	}

	// 构建时间范围
	var startTime time.Time
	now := time.Now()
	
	switch period {
	case "weekly":
		// 本周开始（周一）
		weekday := int(now.Weekday())
		if weekday == 0 { // 周日
			weekday = 7
		}
		startTime = now.AddDate(0, 0, -(weekday-1)).Truncate(24 * time.Hour)
	case "monthly":
		// 本月开始
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "yearly":
		// 今年开始
		startTime = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	default:
		// 默认为本月
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	}

	// 构建查询
	query := s.db.Table("users u").
		Select("u.id, u.username, u.nickname, u.avatar, u.province, u.city, COUNT(c.id) as total_catches, COALESCE(SUM(c.weight), 0) as total_weight, COALESCE(MAX(c.weight), 0) as max_weight").
		Joins("LEFT JOIN catches c ON u.id = c.user_id AND c.deleted_at IS NULL").
		Where("u.is_active = ?", true).
		Group("u.id, u.username, u.nickname, u.avatar, u.province, u.city")

	// 添加时间范围条件
	query = query.Where("c.catch_time >= ?", startTime)

	// 添加地区筛选
	if province != "" {
		query = query.Where("u.province = ?", province)
	}
	if city != "" {
		query = query.Where("u.city = ?", city)
	}

	// 按照总渔获数排序
	query = query.Order("total_catches DESC, total_weight DESC").Limit(limit)

	var rankings []dto.LeaderboardEntry
	if err := query.Scan(&rankings).Error; err != nil {
		return nil, err
	}

	// 添加排名
	for i := range rankings {
		rankings[i].Rank = i + 1
	}

	return &dto.LeaderboardResponse{
		Period:   period,
		Province: province,
		City:     city,
		Rankings: rankings,
		UpdatedAt: now,
	}, nil
}

// GetTopFishers 获取顶级钓手（总体排行）
func (s *LeaderboardService) GetTopFishers(limit int) (*dto.TopFishersResponse, error) {
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	var topFishers []dto.TopFisherEntry
	err := s.db.Table("users").
		Select("id, username, nickname, avatar, province, city, total_catches, total_weight, max_weight, unlocked_species").
		Where("is_active = ? AND total_catches > 0", true).
		Order("total_catches DESC, total_weight DESC, unlocked_species DESC").
		Limit(limit).
		Scan(&topFishers).Error

	if err != nil {
		return nil, err
	}

	// 添加排名
	for i := range topFishers {
		topFishers[i].Rank = i + 1
	}

	return &dto.TopFishersResponse{
		Fishers:   topFishers,
		UpdatedAt: time.Now(),
	}, nil
}

// GetSpeciesLeaderboard 获取单个鱼种的排行榜
func (s *LeaderboardService) GetSpeciesLeaderboard(speciesID uint, limit int) (*dto.SpeciesLeaderboardResponse, error) {
	if limit <= 0 || limit > 50 {
		limit = 10
	}

	// 获取鱼种信息
	var species entities.FishSpecies
	if err := s.db.First(&species, speciesID).Error; err != nil {
		return nil, err
	}

	var rankings []dto.SpeciesRankingEntry
	err := s.db.Table("catches c").
		Select("c.id as catch_id, c.weight, c.length, c.catch_time, c.location_name, u.id as user_id, u.username, u.nickname, u.avatar").
		Joins("JOIN users u ON c.user_id = u.id").
		Where("c.fish_species_id = ? AND c.deleted_at IS NULL AND u.is_active = ?", speciesID, true).
		Where("c.weight IS NOT NULL").
		Order("c.weight DESC, c.length DESC").
		Limit(limit).
		Scan(&rankings).Error

	if err != nil {
		return nil, err
	}

	// 添加排名
	for i := range rankings {
		rankings[i].Rank = i + 1
	}

	return &dto.SpeciesLeaderboardResponse{
		Species: dto.FishSpeciesResponse{
			ID:             species.ID,
			Name:           species.Name,
			EnglishName:    species.EnglishName,
			ScientificName: species.ScientificName,
			Rarity:         species.Rarity,
			Category:       species.Category,
		},
		Rankings:  rankings,
		UpdatedAt: time.Now(),
	}, nil
}

// GetUserRank 获取用户排名
func (s *LeaderboardService) GetUserRank(userID uint, province, city, period, metric string) (*dto.UserRankResponse, error) {
	// 构建时间范围
	var startTime time.Time
	now := time.Now()
	
	switch period {
	case "week":
		weekday := int(now.Weekday())
		if weekday == 0 {
			weekday = 7
		}
		startTime = now.AddDate(0, 0, -(weekday-1)).Truncate(24 * time.Hour)
	case "month":
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	case "year":
		startTime = time.Date(now.Year(), 1, 1, 0, 0, 0, 0, now.Location())
	case "all":
		startTime = time.Time{} // 不限制时间
	default:
		startTime = time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())
	}

	// 根据指标构建不同的排序字段
	var orderField string
	switch metric {
	case "total_catches":
		orderField = "total_catches DESC, total_weight DESC"
	case "unlocked_species":
		orderField = "unlocked_species DESC, total_catches DESC"
	default: // total_weight
		orderField = "total_weight DESC, total_catches DESC"
	}

	// 构建基础查询，获取所有用户的统计数据
	baseQuery := s.db.Table("users u").
		Select("u.id, u.username, u.nickname, u.avatar, u.province, u.city, COUNT(c.id) as total_catches, COALESCE(SUM(c.weight), 0) as total_weight, u.unlocked_species, ROW_NUMBER() OVER (ORDER BY " + orderField + ") as rank").
		Joins("LEFT JOIN catches c ON u.id = c.user_id AND c.deleted_at IS NULL").
		Where("u.is_active = ?", true).
		Group("u.id, u.username, u.nickname, u.avatar, u.province, u.city, u.unlocked_species")

	// 添加时间范围条件
	if !startTime.IsZero() {
		baseQuery = baseQuery.Where("c.catch_time >= ?", startTime)
	}

	// 添加地区筛选
	if province != "" {
		baseQuery = baseQuery.Where("u.province = ?", province)
	}
	if city != "" {
		baseQuery = baseQuery.Where("u.city = ?", city)
	}

	// 使用子查询获取特定用户的排名和周围用户
	var userRankData struct {
		ID               uint    `json:"id"`
		Username         string  `json:"username"`
		Nickname         string  `json:"nickname"`
		Avatar           *string `json:"avatar"`
		Province         *string `json:"province"`
		City             *string `json:"city"`
		TotalCatches     int     `json:"total_catches"`
		TotalWeight      float64 `json:"total_weight"`
		UnlockedSpecies  int     `json:"unlocked_species"`
		Rank             int     `json:"rank"`
	}

	// 获取用户排名数据
	subQuery := "(" + baseQuery.Statement.SQL.String() + ") AS ranked_users"
	err := s.db.Raw("SELECT * FROM "+subQuery+" WHERE id = ?", userID).Scan(&userRankData).Error
	if err != nil {
		return nil, err
	}

	// 获取总用户数
	var totalUsers int64
	countQuery := s.db.Table("users u").
		Joins("LEFT JOIN catches c ON u.id = c.user_id AND c.deleted_at IS NULL").
		Where("u.is_active = ?", true)

	if !startTime.IsZero() {
		countQuery = countQuery.Where("c.catch_time >= ?", startTime)
	}
	if province != "" {
		countQuery = countQuery.Where("u.province = ?", province)
	}
	if city != "" {
		countQuery = countQuery.Where("u.city = ?", city)
	}

	countQuery.Group("u.id").Count(&totalUsers)

	// 获取周围的用户（排名前后各2位）
	var nearbyUsers []dto.LeaderboardEntry
	nearbyQuery := "SELECT * FROM " + subQuery + " WHERE rank BETWEEN ? AND ? ORDER BY rank"
	startRank := userRankData.Rank - 2
	endRank := userRankData.Rank + 2
	if startRank < 1 {
		startRank = 1
	}

	s.db.Raw(nearbyQuery, startRank, endRank).Scan(&nearbyUsers)

	// 构建用户统计信息
	userStats := dto.LeaderboardEntry{
		Rank:            userRankData.Rank,
		UserID:          userRankData.ID,
		Username:        userRankData.Username,
		Nickname:        userRankData.Nickname,
		Avatar:          userRankData.Avatar,
		Province:        userRankData.Province,
		City:            userRankData.City,
		TotalCatches:    userRankData.TotalCatches,
		TotalWeight:     userRankData.TotalWeight,
		UnlockedSpecies: userRankData.UnlockedSpecies,
	}

	return &dto.UserRankResponse{
		Rank:        userRankData.Rank,
		TotalUsers:  int(totalUsers),
		UserStats:   userStats,
		NearbyUsers: nearbyUsers,
		Period:      period,
		Metric:      metric,
		Province:    province,
		City:        city,
		UpdatedAt:   now,
	}, nil
}