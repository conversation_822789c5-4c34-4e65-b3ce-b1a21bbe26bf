package dto

import "time"

// CreateCatchRequest 创建渔获记录请求
type CreateCatchRequest struct {
	FishSpeciesID     uint       `json:"fish_species_id" validate:"required"`
	Weight            *float64   `json:"weight,omitempty"`
	Length            *float64   `json:"length,omitempty"`
	CatchTime         time.Time  `json:"catch_time" validate:"required"`
	Latitude          *float64   `json:"latitude,omitempty"`
	Longitude         *float64   `json:"longitude,omitempty"`
	LocationName      *string    `json:"location_name,omitempty"`
	Weather           *string    `json:"weather,omitempty"`
	Temperature       *float64   `json:"temperature,omitempty"`
	BaitUsed          *string    `json:"bait_used,omitempty"`
	TechniqueUsed     *string    `json:"technique_used,omitempty"`
	PhotoURL          *string    `json:"photo_url,omitempty"`
	ProcessedPhotoURL *string    `json:"processed_photo_url,omitempty"`
	Notes             *string    `json:"notes,omitempty"`
	IsPublic          *bool      `json:"is_public,omitempty"`
}

// UpdateCatchRequest 更新渔获记录请求
type UpdateCatchRequest struct {
	Weight            *float64  `json:"weight,omitempty"`
	Length            *float64  `json:"length,omitempty"`
	CatchTime         *time.Time `json:"catch_time,omitempty"`
	Latitude          *float64  `json:"latitude,omitempty"`
	Longitude         *float64  `json:"longitude,omitempty"`
	LocationName      *string   `json:"location_name,omitempty"`
	Weather           *string   `json:"weather,omitempty"`
	Temperature       *float64  `json:"temperature,omitempty"`
	BaitUsed          *string   `json:"bait_used,omitempty"`
	TechniqueUsed     *string   `json:"technique_used,omitempty"`
	PhotoURL          *string   `json:"photo_url,omitempty"`
	ProcessedPhotoURL *string   `json:"processed_photo_url,omitempty"`
	Notes             *string   `json:"notes,omitempty"`
	IsPublic          *bool     `json:"is_public,omitempty"`
}

// CatchResponse 渔获记录响应
type CatchResponse struct {
	ID                uint                `json:"id"`
	UserID            uint                `json:"user_id"`
	FishSpeciesID     uint                `json:"fish_species_id"`
	Weight            *float64            `json:"weight"`
	Length            *float64            `json:"length"`
	CatchTime         *time.Time          `json:"catch_time"`
	Latitude          *float64            `json:"latitude"`
	Longitude         *float64            `json:"longitude"`
	LocationName      *string             `json:"location_name"`
	Weather           *string             `json:"weather"`
	Temperature       *float64            `json:"temperature"`
	BaitUsed          *string             `json:"bait_used"`
	TechniqueUsed     *string             `json:"technique_used"`
	PhotoURL          *string             `json:"photo_url"`
	ProcessedPhotoURL *string             `json:"processed_photo_url"`
	Notes             *string             `json:"notes"`
	IsPublic          bool                `json:"is_public"`
	IsVerified        bool                `json:"is_verified"`
	AIProcessed       bool                `json:"ai_processed"`
	AIConfidence      *float64            `json:"ai_confidence"`
	CreatedAt         time.Time           `json:"created_at"`
	UpdatedAt         time.Time           `json:"updated_at"`
	User              *UserResponse       `json:"user,omitempty"`
	FishSpecies       *FishSpeciesResponse `json:"fish_species,omitempty"`
}

// FishSpeciesResponse 鱼种响应
type FishSpeciesResponse struct {
	ID             uint     `json:"id"`
	Name           string   `json:"name"`
	EnglishName    *string  `json:"english_name"`
	ScientificName *string  `json:"scientific_name"`
	Family         *string  `json:"family"`
	Description    *string  `json:"description"`
	Habitat        *string  `json:"habitat"`
	Distribution   *string  `json:"distribution"`
	Color          *string  `json:"color"`
	Shape          *string  `json:"shape"`
	MaxLength      *float64 `json:"max_length"`
	MaxWeight      *float64 `json:"max_weight"`
	ImageURL       *string  `json:"image_url"`
	ThumbnailURL   *string  `json:"thumbnail_url"`
	Rarity         string   `json:"rarity"`
	Category       string   `json:"category"`
	IsActive       bool     `json:"is_active"`
}

// CatchListResponse 渔获记录列表响应
type CatchListResponse struct {
	Data       []CatchResponse `json:"data"`
	Total      int64           `json:"total"`
	Page       int             `json:"page"`
	PageSize   int             `json:"page_size"`
	TotalPages int             `json:"total_pages"`
}