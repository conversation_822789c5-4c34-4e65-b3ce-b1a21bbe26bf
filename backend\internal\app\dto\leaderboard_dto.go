package dto

import "time"

// LeaderboardResponse 区域排行榜响应
type LeaderboardResponse struct {
	Period   string             `json:"period"`   // weekly, monthly, yearly
	Province string             `json:"province"` // 省份
	City     string             `json:"city"`     // 城市
	Rankings []LeaderboardEntry `json:"rankings"`
	UpdatedAt time.Time         `json:"updated_at"`
}

// LeaderboardEntry 排行榜条目
type LeaderboardEntry struct {
	Rank         int     `json:"rank"`
	UserID       uint    `json:"user_id"`
	Username     string  `json:"username"`
	Nickname     string  `json:"nickname"`
	Avatar       *string `json:"avatar"`
	Province     *string `json:"province"`
	City         *string `json:"city"`
	TotalCatches int     `json:"total_catches"`
	TotalWeight     float64 `json:"total_weight"`
	MaxWeight       float64 `json:"max_weight"`
	UnlockedSpecies int     `json:"unlocked_species"`
}

// TopFishersResponse 顶级钓手响应
type TopFishersResponse struct {
	Fishers   []TopFisherEntry `json:"fishers"`
	UpdatedAt time.Time        `json:"updated_at"`
}

// TopFisherEntry 顶级钓手条目
type TopFisherEntry struct {
	Rank            int     `json:"rank"`
	UserID          uint    `json:"user_id"`
	Username        string  `json:"username"`
	Nickname        string  `json:"nickname"`
	Avatar          *string `json:"avatar"`
	Province        *string `json:"province"`
	City            *string `json:"city"`
	TotalCatches    int     `json:"total_catches"`
	TotalWeight     float64 `json:"total_weight"`
	MaxWeight       float64 `json:"max_weight"`
	UnlockedSpecies int     `json:"unlocked_species"`
}

// SpeciesLeaderboardResponse 鱼种排行榜响应
type SpeciesLeaderboardResponse struct {
	Species   FishSpeciesResponse    `json:"species"`
	Rankings  []SpeciesRankingEntry  `json:"rankings"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// SpeciesRankingEntry 鱼种排行榜条目
type SpeciesRankingEntry struct {
	Rank         int        `json:"rank"`
	CatchID      uint       `json:"catch_id"`
	UserID       uint       `json:"user_id"`
	Username     string     `json:"username"`
	Nickname     string     `json:"nickname"`
	Avatar       *string    `json:"avatar"`
	Weight       *float64   `json:"weight"`
	Length       *float64   `json:"length"`
	CatchTime    *time.Time `json:"catch_time"`
	LocationName *string    `json:"location_name"`
}

// UserRankResponse 用户排名响应
type UserRankResponse struct {
	Rank        int                 `json:"rank"`         // 用户当前排名
	TotalUsers  int                 `json:"total_users"`  // 总参与用户数
	UserStats   LeaderboardEntry    `json:"user_stats"`   // 用户统计信息
	NearbyUsers []LeaderboardEntry  `json:"nearby_users"` // 附近排名的用户
	Period      string              `json:"period"`       // 统计周期
	Metric      string              `json:"metric"`       // 排名指标
	Province    string              `json:"province"`     // 省份筛选
	City        string              `json:"city"`         // 城市筛选
	UpdatedAt   time.Time           `json:"updated_at"`   // 更新时间
}