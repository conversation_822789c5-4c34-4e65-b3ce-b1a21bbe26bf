package entities

// Location 地理位置模型（如果需要的话）
type Location struct {
	BaseModel
	Name      string   `gorm:"not null;type:varchar(255)" json:"name"`       // 地点名称
	Province  string   `gorm:"type:varchar(100)" json:"province"`            // 省份
	City      string   `gorm:"type:varchar(100)" json:"city"`                // 城市
	District  string   `gorm:"type:varchar(100)" json:"district"`            // 区县
	Latitude  *float64 `json:"latitude"`                                     // 纬度
	Longitude *float64 `json:"longitude"`                                    // 经度
	Type      string   `gorm:"type:varchar(50);default:'lake'" json:"type"`  // 类型: lake, river, sea, pond
	IsActive  bool     `gorm:"default:true" json:"is_active"`                // 是否可用
}

// TableName 指定表名
func (Location) TableName() string {
	return "locations"
}