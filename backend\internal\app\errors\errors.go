package errors

import "net/http"

// ErrorType 错误类型
type ErrorType string

const (
	ErrorTypeValidation    ErrorType = "VALIDATION_ERROR"
	ErrorTypeNotFound      ErrorType = "NOT_FOUND"
	ErrorTypeUnauthorized  ErrorType = "UNAUTHORIZED"
	ErrorTypeForbidden     ErrorType = "FORBIDDEN"
	ErrorTypeConflict      ErrorType = "CONFLICT"
	ErrorTypeInternal      ErrorType = "INTERNAL_ERROR"
	ErrorTypeBadRequest    ErrorType = "BAD_REQUEST"
	ErrorTypeServiceUnavailable ErrorType = "SERVICE_UNAVAILABLE"
)

// AppError 应用错误结构
type AppError struct {
	Code    int       `json:"code"`
	Message string    `json:"message"`
	Type    ErrorType `json:"type"`
	Details interface{} `json:"details,omitempty"`
}

func (e *AppError) Error() string {
	return e.Message
}

// NewAppError 创建新的应用错误
func NewAppError(code int, message string, errorType ErrorType) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Type:    errorType,
	}
}

// NewValidationError 创建验证错误
func NewValidationError(message string) *AppError {
	return NewAppError(http.StatusBadRequest, message, ErrorTypeValidation)
}

// NewNotFoundError 创建未找到错误
func NewNotFoundError(message string) *AppError {
	return NewAppError(http.StatusNotFound, message, ErrorTypeNotFound)
}

// NewUnauthorizedError 创建未授权错误
func NewUnauthorizedError(message string) *AppError {
	return NewAppError(http.StatusUnauthorized, message, ErrorTypeUnauthorized)
}

// NewForbiddenError 创建禁止访问错误
func NewForbiddenError(message string) *AppError {
	return NewAppError(http.StatusForbidden, message, ErrorTypeForbidden)
}

// NewConflictError 创建冲突错误
func NewConflictError(message string) *AppError {
	return NewAppError(http.StatusConflict, message, ErrorTypeConflict)
}

// NewInternalError 创建内部服务器错误
func NewInternalError(message string) *AppError {
	return NewAppError(http.StatusInternalServerError, message, ErrorTypeInternal)
}

// NewBadRequestError 创建错误请求错误
func NewBadRequestError(message string) *AppError {
	return NewAppError(http.StatusBadRequest, message, ErrorTypeBadRequest)
}

// NewServiceUnavailableError 创建服务不可用错误
func NewServiceUnavailableError(message string) *AppError {
	return NewAppError(http.StatusServiceUnavailable, message, ErrorTypeServiceUnavailable)
}

// WithDetails 添加错误详情
func (e *AppError) WithDetails(details interface{}) *AppError {
	e.Details = details
	return e
}